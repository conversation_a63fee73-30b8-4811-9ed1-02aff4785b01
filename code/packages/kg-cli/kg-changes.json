{"timestamp": "2025-06-02T06:28:20.731Z", "summary": {"nodesAdded": 2, "nodesUpdated": 2, "nodesMarkedStale": 0, "edgesAdded": 1, "edgesUpdated": 2, "edgesMarkedStale": 0}, "coverage": [], "errors": [{"message": "Invalid @implements format: \"milestone-M1.2#Component${i\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#Component${i\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#Component${i\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}]}